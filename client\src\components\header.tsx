import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Mic } from "lucide-react";

export default function Header() {
  const [isOpen, setIsOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setIsOpen(false);
    }
  };

  const navigation = [
    { name: "Features", href: "features" },
    { name: "How It Works", href: "how-it-works" },
    { name: "Download", href: "download" },
    { name: "Support", href: "support" },
  ];

  return (
    <header className="bg-gradient-to-r from-slate-900/95 to-blue-900/95 backdrop-blur-md shadow-xl sticky top-0 z-50 border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 brand-gradient rounded-xl flex items-center justify-center">
              <Mic className="text-white text-lg" size={20} />
            </div>
            <span className="text-2xl font-bold text-white">Assistant724</span>
          </div>
          
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className="text-gray-300 hover:text-white transition-colors font-medium"
              >
                {item.name}
              </button>
            ))}
          </nav>

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" className="text-gray-300 hover:text-white">
                <Menu size={24} />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <nav className="flex flex-col space-y-4 mt-8">
                {navigation.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => scrollToSection(item.href)}
                    className="text-left text-lg text-gray-600 hover:text-blue-600 transition-colors font-medium py-2"
                  >
                    {item.name}
                  </button>
                ))}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
