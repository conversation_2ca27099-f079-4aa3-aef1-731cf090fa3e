import { Mic } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const quickLinks = [
    { name: "Features", href: "features" },
    { name: "How It Works", href: "how-it-works" },
    { name: "Download", href: "download" },
    { name: "Privacy Policy", href: "#" },
    { name: "Terms of Service", href: "#" }
  ];

  const supportLinks = [
    { name: "<EMAIL>", href: "mailto:<EMAIL>" },
    { name: "Help Center", href: "#" },
    { name: "Contact Us", href: "#" },
    { name: "Bug Reports", href: "#" },
    { name: "Feature Requests", href: "#" }
  ];

  const socialLinks = [
    { name: "Twitter", href: "#", icon: "fab fa-twitter" },
    { name: "Facebook", href: "#", icon: "fab fa-facebook" },
    { name: "LinkedIn", href: "#", icon: "fab fa-linkedin" },
    { name: "YouTube", href: "#", icon: "fab fa-youtube" }
  ];

  return (
    <footer id="support" className="bg-gradient-to-br from-slate-900 to-blue-900 text-white py-16 border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 brand-gradient rounded-xl flex items-center justify-center">
                <Mic className="text-white text-xl" size={24} />
              </div>
              <span className="text-2xl font-bold">Assistant724</span>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
              Transform your mobile productivity with AI-powered voice assistance. Seamlessly integrated with all your favorite Android applications.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="ghost"
                  size="sm"
                  className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-colors p-0"
                  onClick={() => console.log(`${social.name} clicked`)}
                >
                  <i className={social.icon}></i>
                </Button>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  {link.href.startsWith('#') && link.href !== '#' ? (
                    <button
                      onClick={() => scrollToSection(link.href.substring(1))}
                      className="text-gray-300 hover:text-white transition-colors text-left"
                    >
                      {link.name}
                    </button>
                  ) : (
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </a>
                  )}
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Support</h4>
            <ul className="space-y-3">
              {supportLinks.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 Assistant724. All rights reserved. Made with ❤️ for productivity enthusiasts.
          </p>
        </div>
      </div>
    </footer>
  );
}
