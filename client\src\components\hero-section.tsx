import { Button } from "@/components/ui/button";
import { Play, Download } from "lucide-react";

export default function HeroSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section className="relative brand-gradient-purple py-20 lg:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-10"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <div className="inline-block bg-white bg-opacity-20 backdrop-blur text-white px-6 py-4 rounded-2xl text-center font-bold mb-6 border border-white border-opacity-30">
              <div className="text-2xl mb-1">🏆 The #1 Assistant</div>
              <div className="text-lg">Compatible with All Your Apps</div>
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
              Elevate Yourself to a{" "}
              <span className="text-yellow-300">Superior Version</span>
            </h1>
            <p className="text-xl text-purple-100 mb-8 leading-relaxed">
              Transform your voice into brilliant, creative messages that captivate and inspire. Assistant724 revolutionizes how you communicate across every Android app - from YouTube comments to emails, unlock your most impressive self.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                onClick={() => scrollToSection("download")}
                className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <Download className="mr-3" size={20} />
                Download on Google Play
              </Button>
              <Button
                variant="outline"
                onClick={() => scrollToSection("how-it-works")}
                className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-300"
              >
                <Play className="mr-3" size={20} />
                See How It Works
              </Button>
            </div>
          </div>
          <div className="flex justify-center lg:justify-end">
            <img
              src="https://images.unsplash.com/photo-1565106430482-8f6e74349ca1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=800"
              alt="Assistant724 app interface on smartphone"
              className="w-80 h-auto max-w-full drop-shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
