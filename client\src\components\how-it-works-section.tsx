import { Card, CardContent } from "@/components/ui/card";
import { MessageCircle, Mail, FileText, Youtube, Search, Share2 } from "lucide-react";

export default function HowItWorksSection() {
  const steps = [
    {
      number: 1,
      title: "Tap Any Text Field",
      description: "Open any app - YouTube, WhatsApp, email, or notes. When you tap a text field, <PERSON><PERSON><PERSON> magically appears as your personal microphone assistant.",
      image: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      bgColor: "bg-blue-500"
    },
    {
      number: 2,
      title: "Speak Your Thoughts",
      description: "Tap the Assistant724 microphone and speak naturally. Our AI doesn't just transcribe - it transforms your ideas into brilliant, compelling messages.",
      image: "https://images.unsplash.com/photo-1590602847861-f357a9332bbc?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      bgColor: "bg-emerald-500"
    },
    {
      number: 3,
      title: "Choose Your Perfect Message", 
      description: "Assistant724 presents you with creative, engaging options. Select the most impactful version or let AI craft the perfect message that represents your best self.",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300",
      bgColor: "bg-purple-500"
    }
  ];

  const useCases = [
    { icon: MessageCircle, text: "Craft brilliant replies that impress" },
    { icon: Mail, text: "Write professional emails effortlessly" },
    { icon: FileText, text: "Transform thoughts into clear notes" },
    { icon: Youtube, text: "Create engaging video comments" },
    { icon: Search, text: "Express complex searches naturally" },
    { icon: Share2, text: "Design captivating social posts" }
  ];

  return (
    <section id="how-it-works" className="py-20 bg-gradient-to-br from-slate-900/90 to-blue-900/90 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Unlock Your Communication Superpower</h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Transform into your most articulate self in just three simple steps. Watch ordinary thoughts become extraordinary messages.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {steps.map((step) => (
            <div key={step.number} className="text-center">
              <div className="relative mb-8">
                <img
                  src={step.image}
                  alt={step.title}
                  className="w-full h-64 object-cover rounded-2xl shadow-lg"
                />
                <div className={`absolute -top-4 -left-4 w-12 h-12 ${step.bgColor} text-white rounded-full flex items-center justify-center font-bold text-xl shadow-lg`}>
                  {step.number}
                </div>
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">{step.title}</h3>
              <p className="text-blue-100 leading-relaxed">{step.description}</p>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Card className="bg-white/10 backdrop-blur-md p-8 shadow-2xl shadow-blue-500/20 max-w-4xl mx-auto border border-white/20 hover:border-white/40 transition-all duration-500">
            <CardContent className="p-0">
              <h3 className="text-2xl font-semibold text-white mb-6">Use Cases</h3>
              <div className="grid md:grid-cols-2 gap-6 text-left">
                <div className="space-y-3">
                  {useCases.slice(0, 3).map((useCase, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <useCase.icon className="text-blue-400" size={20} />
                      <span className="text-blue-100">{useCase.text}</span>
                    </div>
                  ))}
                </div>
                <div className="space-y-3">
                  {useCases.slice(3).map((useCase, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <useCase.icon className="text-blue-400" size={20} />
                      <span className="text-blue-100">{useCase.text}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
