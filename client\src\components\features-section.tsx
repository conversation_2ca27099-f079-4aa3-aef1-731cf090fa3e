import { Card, CardContent } from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Zap, 
  Shield, 
  Languages, 
  Settings 
} from "lucide-react";

export default function FeaturesSection() {
  const features = [
    {
      icon: Mic,
      title: "Universal Voice Magic",
      description: "Instantly appear as a microphone in any app - YouTube, WhatsApp, email, or notes. Tap once and watch your voice transform into compelling, creative text.",
      gradient: "from-blue-50 to-indigo-50",
      border: "border-blue-100",
      iconBg: "from-blue-500 to-blue-600"
    },
    {
      icon: Brain,
      title: "Intelligent Creative Enhancement",
      description: "Advanced AI doesn't just transcribe - it elevates your thoughts into more engaging, informative, and impactful messages that truly represent your best self.",
      gradient: "from-green-50 to-emerald-50",
      border: "border-green-100",
      iconBg: "from-emerald-500 to-green-600"
    },
    {
      icon: Zap,
      title: "Instant Transformation",
      description: "Watch your voice become brilliant text in real-time. No delays, no waiting - just immediate elevation of your communication across any platform.",
      gradient: "from-purple-50 to-violet-50",
      border: "border-purple-100",
      iconBg: "from-purple-500 to-violet-600"
    },
    {
      icon: Shield,
      title: "Fortress-Level Privacy",
      description: "Your personal thoughts remain yours. Advanced encryption protects every word while our AI works its magic - we never store or share your conversations.",
      gradient: "from-orange-50 to-amber-50",
      border: "border-orange-100",
      iconBg: "from-amber-500 to-orange-600"
    },
    {
      icon: Languages,
      title: "Global Communication Power",
      description: "Break language barriers effortlessly. Automatically detects and optimizes your message style across multiple languages and cultural contexts.",
      gradient: "from-pink-50 to-rose-50",
      border: "border-pink-100",
      iconBg: "from-pink-500 to-rose-600"
    },
    {
      icon: Settings,
      title: "Invisible Integration",
      description: "Works like magic behind the scenes. Appears exactly when you need it in any app, enhancing your communication without interrupting your workflow.",
      gradient: "from-teal-50 to-cyan-50",
      border: "border-teal-100",
      iconBg: "from-teal-500 to-cyan-600"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gradient-to-br from-blue-900/90 to-indigo-900/90 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">
            Revolutionary Features That Transform Your Communication
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Assistant724 amplifies your creativity across every Android app, delivering intelligent voice-to-text magic that makes every message more impactful and engaging.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature) => (
            <Card 
              key={feature.title}
              className={`bg-white/10 backdrop-blur-md p-8 hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-500 border border-white/20 hover:border-white/40 transform hover:-translate-y-2 hover:scale-105`}
            >
              <CardContent className="p-0">
                <div className={`w-16 h-16 bg-gradient-to-br ${feature.iconBg} rounded-xl flex items-center justify-center mb-6`}>
                  <feature.icon className="text-white text-2xl" size={24} />
                </div>
                <h3 className="text-2xl font-semibold text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-blue-100 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
