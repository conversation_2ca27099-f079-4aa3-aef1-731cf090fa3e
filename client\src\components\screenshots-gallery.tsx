export default function ScreenshotsGallery() {
  const screenshots = [
    {
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&h=600",
      title: "Messaging Apps",
      alt: "Assistant724 in messaging app"
    },
    {
      image: "https://images.unsplash.com/photo-1596558450268-9c27524ba856?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&h=600",
      title: "Email Apps",
      alt: "Assistant724 in email app"
    },
    {
      image: "https://images.unsplash.com/photo-1611162617474-5b21e879e113?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&h=600",
      title: "Social Media",
      alt: "Assistant724 in social media app"
    },
    {
      image: "https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&h=600",
      title: "Note Taking",
      alt: "Assistant724 in notes app"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-900/90 to-purple-900/90 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">See Assistant724 in Action</h2>
          <p className="text-xl text-purple-100">
            Experience the magical integration across different Android applications
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {screenshots.map((screenshot, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-md p-4 rounded-2xl shadow-2xl shadow-purple-500/20 border border-white/20 hover:border-white/40 transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
              <img
                src={screenshot.image}
                alt={screenshot.alt}
                className="w-full h-auto rounded-xl"
              />
              <h4 className="text-lg font-semibold text-white mt-4 text-center">
                {screenshot.title}
              </h4>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
