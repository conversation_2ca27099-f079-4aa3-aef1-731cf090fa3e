import { But<PERSON> } from "@/components/ui/button";
import { Download, Smartphone } from "lucide-react";

export default function DownloadSection() {
  const handleDownload = (type: 'playstore' | 'apk') => {
    // In a real implementation, these would be actual download links
    console.log(`Download ${type} clicked`);
  };

  return (
    <section id="download" className="py-20 brand-gradient-purple relative overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-10"></div>
      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
          Ready to Become Your Most Impressive Self?
        </h2>
        <p className="text-xl text-purple-100 mb-12 leading-relaxed">
          Join thousands who have elevated their communication game with Assistant724. Download now and unlock the superior version of yourself in every conversation.
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <Button
            onClick={() => handleDownload('playstore')}
            className="inline-flex items-center px-8 py-4 bg-black text-white font-semibold rounded-xl hover:bg-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 min-w-[200px]"
          >
            <Smartphone className="text-2xl mr-4" size={24} />
            <div className="text-left">
              <div className="text-xs text-gray-300">GET IT ON</div>
              <div className="text-lg font-bold">Google Play</div>
            </div>
          </Button>

          <Button
            variant="outline"
            onClick={() => handleDownload('apk')}
            className="inline-flex items-center px-8 py-4 bg-white bg-opacity-20 backdrop-blur text-white font-semibold rounded-xl hover:bg-opacity-30 transition-all duration-300 border border-white border-opacity-30 min-w-[200px]"
          >
            <Download className="text-2xl mr-4" size={24} />
            <div className="text-left">
              <div className="text-xs text-purple-200">DIRECT</div>
              <div className="text-lg font-bold">Download APK</div>
            </div>
          </Button>
        </div>

        <div className="mt-12 grid md:grid-cols-3 gap-8 text-center">
          <div className="bg-white bg-opacity-10 backdrop-blur p-6 rounded-xl border border-white border-opacity-20">
            <div className="text-3xl font-bold text-white mb-2">50K+</div>
            <div className="text-purple-200">Active Users</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-blur p-6 rounded-xl border border-white border-opacity-20">
            <div className="text-3xl font-bold text-white mb-2">4.8</div>
            <div className="text-purple-200">App Store Rating</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-blur p-6 rounded-xl border border-white border-opacity-20">
            <div className="text-3xl font-bold text-white mb-2">99.9%</div>
            <div className="text-purple-200">Uptime</div>
          </div>
        </div>
      </div>
    </section>
  );
}
