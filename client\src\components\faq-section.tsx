import { Card, CardContent } from "@/components/ui/card";

export default function FaqSection() {
  const faqs = [
    {
      question: "Is Assistant724 compatible with all Android apps?",
      answer: "Yes, Assistant724 works with virtually all Android applications that have text input fields. It integrates seamlessly through Android's accessibility services."
    },
    {
      question: "Does Assistant724 require internet connection?",
      answer: "Assistant724 requires an internet connection for optimal AI-powered speech recognition. However, basic functionality may work offline with reduced accuracy."
    },
    {
      question: "Is my voice data secure and private?",
      answer: "Absolutely. We use end-to-end encryption for all voice data processing. Your conversations are never stored permanently and are processed securely."
    },
    {
      question: "What languages does Assistant724 support?",
      answer: "Assistant724 currently supports English, Spanish, French, German, Italian, and more languages are being added regularly. The app automatically detects your system language."
    },
    {
      question: "How much battery does Assistant724 consume?",
      answer: "Assistant724 is optimized for minimal battery usage. It only activates when you explicitly use the voice input feature and remains dormant otherwise."
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900/90 to-slate-900/90 backdrop-blur-sm">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          <p className="text-xl text-blue-100">
            Everything you need to know about Assistant724
          </p>
        </div>

        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <Card key={index} className="bg-white/10 backdrop-blur-md p-6 shadow-2xl shadow-blue-500/20 border border-white/20 hover:border-white/40 transition-all duration-500 transform hover:-translate-y-1">
              <CardContent className="p-0">
                <h3 className="text-lg font-semibold text-white mb-3">
                  {faq.question}
                </h3>
                <p className="text-blue-100">
                  {faq.answer}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
