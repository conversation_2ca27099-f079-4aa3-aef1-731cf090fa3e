# Assistant724 - AI Voice Assistant App

## Overview

Assistant724 is a modern full-stack web application for an AI-powered voice assistant mobile app landing page. The application showcases the features of a voice-to-text Android application that integrates with all Android apps. It's built with a React frontend, Node.js/Express backend, and is designed for deployment on Replit's platform.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript throughout the entire stack
- **API Design**: RESTful API structure with `/api` prefix
- **Development**: TSX for running TypeScript directly in development

### Database Layer
- **ORM**: Drizzle ORM for type-safe database operations
- **Database**: PostgreSQL with Neon serverless driver
- **Schema**: Shared schema definitions between frontend and backend
- **Migrations**: Drizzle Kit for database migrations

## Key Components

### Frontend Components
- **Landing Page**: Single-page application with multiple sections
  - Header with navigation
  - Hero section with call-to-action
  - Features showcase
  - How-it-works explanation
  - Screenshots gallery
  - Download section
  - FAQ section
  - Footer with contact information

### Backend Structure
- **Server Entry**: Express server with middleware setup
- **Routes**: Centralized route registration system
- **Storage**: Abstracted storage interface with in-memory implementation
- **Vite Integration**: Development server with HMR support

### Shared Resources
- **Schema**: Database schema definitions with Zod validation
- **Types**: Shared TypeScript types between frontend and backend

## Data Flow

1. **Client Requests**: Frontend makes API calls using TanStack Query
2. **API Processing**: Express server handles requests through registered routes
3. **Data Storage**: Storage abstraction layer manages data operations
4. **Response**: JSON responses sent back to client with error handling

## External Dependencies

### UI/UX Libraries
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Pre-built component library

### Development Tools
- **Vite**: Build tool with React plugin and runtime error overlay
- **TypeScript**: Type safety throughout the application
- **ESBuild**: Fast bundling for production builds

### Database & Backend
- **Neon Database**: Serverless PostgreSQL provider
- **Drizzle ORM**: Type-safe database toolkit
- **Express**: Minimal web framework for Node.js

## Deployment Strategy

### Replit Configuration
- **Environment**: Node.js 20, Web, and PostgreSQL 16 modules
- **Development**: `npm run dev` for local development
- **Production**: `npm run build` followed by `npm run start`
- **Auto-scaling**: Configured for Replit's autoscale deployment target

### Build Process
1. **Frontend Build**: Vite builds React app to `dist/public`
2. **Backend Build**: ESBuild bundles server code to `dist/index.js`
3. **Static Assets**: Frontend assets served by Express in production

### Environment Setup
- **Database URL**: Required environment variable for PostgreSQL connection
- **Port Configuration**: Express server runs on port 5000
- **Static Files**: Served from `dist/public` in production mode

## Changelog

```
Changelog:
- June 26, 2025. Initial setup
- June 26, 2025. Enhanced content with more engaging, creative messaging:
  * Updated hero section with "#1 Assistant Compatible with All Your Apps" badge
  * Replaced "seamless" repetition with varied, impactful language
  * Added emphasis on creative enhancement and superior communication
  * Updated features to focus on transformation and elevation
  * Revised "How It Works" section to highlight AI's creative capabilities
  * Enhanced download section with "become your most impressive self" messaging
- June 26, 2025. Transformed UI to navy blue gradient theme with 3D effects:
  * Changed entire homepage background to navy blue gradients
  * Updated header with glass morphism effect and white text
  * Converted all cards to floating 3D style with backdrop blur
  * Added hover animations with lift and scale effects
  * Applied gradient backgrounds across all sections
  * Enhanced visual depth with shadow effects and border highlights
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```